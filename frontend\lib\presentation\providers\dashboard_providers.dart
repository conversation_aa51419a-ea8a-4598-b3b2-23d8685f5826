import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/models/dashboard.dart';
import '../../data/models/alert.dart';
import '../../data/models/activity.dart';
import '../../data/models/api_response.dart';
import '../../data/repositories/dashboard_repository.dart';

// Dashboard Repository Provider
final dashboardRepositoryProvider = Provider<DashboardRepository>((ref) {
  return DashboardRepository();
});

// Dashboard Overview Provider with fallback
final dashboardOverviewProvider = FutureProvider.family<DashboardOverview, DashboardParams>((ref, params) async {
  try {
    final repository = ref.read(dashboardRepositoryProvider);
    final response = await repository.getDashboardOverview(
      propertyIds: params.propertyIds,
      timeRange: params.timeRange,
    );

    if (response.success && response.data != null) {
      return response.data!;
    } else {
      // Return mock data for demonstration when API fails
      return _createMockDashboardOverview();
    }
  } catch (e) {
    // Return mock data for demonstration when there's an error
    return _createMockDashboardOverview();
  }
});

// Dashboard Alerts Provider with fallback
final dashboardAlertsProvider = FutureProvider.family<List<Alert>, AlertParams>((ref, params) async {
  try {
    final repository = ref.read(dashboardRepositoryProvider);
    final response = await repository.getDashboardAlerts(
      page: params.page,
      limit: params.limit,
      severity: params.severity,
      status: params.status,
      propertyId: params.propertyId,
    );

    if (response.success && response.data != null) {
      return response.data!.data;
    } else {
      // Return empty list when API fails
      return [];
    }
  } catch (e) {
    // Return empty list when there's an error
    return [];
  }
});

// Dashboard Statistics Provider
final dashboardStatsProvider = Provider.family<Map<String, dynamic>, DashboardOverview>((ref, overview) {
  return _calculateDashboardStats(overview);
});

// Dashboard Refresh State Provider
final dashboardRefreshProvider = StateNotifierProvider<DashboardRefreshNotifier, bool>((ref) {
  return DashboardRefreshNotifier();
});

// Dashboard Version Toggle Provider
final dashboardVersionProvider = StateNotifierProvider<DashboardVersionNotifier, bool>((ref) {
  return DashboardVersionNotifier();
});

// Auto Refresh Provider
final autoRefreshProvider = StateNotifierProvider<AutoRefreshNotifier, bool>((ref) {
  return AutoRefreshNotifier();
});

// Dashboard Time Range Provider
final dashboardTimeRangeProvider = StateNotifierProvider<DashboardTimeRangeNotifier, String>((ref) {
  return DashboardTimeRangeNotifier();
});

// Property Filter Provider
final propertyFilterProvider = StateNotifierProvider<PropertyFilterNotifier, List<String>?>((ref) {
  return PropertyFilterNotifier();
});

// Dashboard Parameters Class
class DashboardParams {
  final List<String>? propertyIds;
  final String timeRange;

  const DashboardParams({
    this.propertyIds,
    this.timeRange = '24h',
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DashboardParams &&
          runtimeType == other.runtimeType &&
          propertyIds == other.propertyIds &&
          timeRange == other.timeRange;

  @override
  int get hashCode => propertyIds.hashCode ^ timeRange.hashCode;
}

// Alert Parameters Class
class AlertParams {
  final int page;
  final int limit;
  final String? severity;
  final String? status;
  final String? propertyId;

  const AlertParams({
    this.page = 1,
    this.limit = 20,
    this.severity,
    this.status,
    this.propertyId,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AlertParams &&
          runtimeType == other.runtimeType &&
          page == other.page &&
          limit == other.limit &&
          severity == other.severity &&
          status == other.status &&
          propertyId == other.propertyId;

  @override
  int get hashCode =>
      page.hashCode ^
      limit.hashCode ^
      severity.hashCode ^
      status.hashCode ^
      propertyId.hashCode;
}

// State Notifiers
class DashboardRefreshNotifier extends StateNotifier<bool> {
  DashboardRefreshNotifier() : super(false);

  Future<void> refresh() async {
    state = true;
    // Simulate refresh delay
    await Future.delayed(const Duration(milliseconds: 500));
    state = false;
  }
}

class DashboardVersionNotifier extends StateNotifier<bool> {
  DashboardVersionNotifier() : super(false); // false = V1, true = V2

  void toggleVersion() {
    state = !state;
  }

  void setVersion(bool isV2) {
    state = isV2;
  }
}

class AutoRefreshNotifier extends StateNotifier<bool> {
  AutoRefreshNotifier() : super(false);

  void toggle() {
    state = !state;
  }

  void setEnabled(bool enabled) {
    state = enabled;
  }
}

class DashboardTimeRangeNotifier extends StateNotifier<String> {
  DashboardTimeRangeNotifier() : super('24h');

  void setTimeRange(String timeRange) {
    state = timeRange;
  }
}

class PropertyFilterNotifier extends StateNotifier<List<String>?> {
  PropertyFilterNotifier() : super(null);

  void setPropertyFilter(List<String>? propertyIds) {
    state = propertyIds;
  }

  void clearFilter() {
    state = null;
  }
}

// Helper function to calculate dashboard statistics
Map<String, dynamic> _calculateDashboardStats(DashboardOverview overview) {
  final properties = overview.properties;
  final alerts = overview.recentAlerts;
  
  // Property statistics
  final totalProperties = properties.length;
  final healthyProperties = properties.where((p) => p.status == 'OPERATIONAL').length;
  final warningProperties = properties.where((p) => p.status == 'WARNING').length;
  final criticalProperties = properties.where((p) => p.status == 'CRITICAL').length;
  final offlineProperties = properties.where((p) => p.status == 'OFFLINE').length;
  
  // Alert statistics
  final totalAlerts = alerts.length;
  final criticalAlerts = alerts.where((a) => a.severity == 'CRITICAL').length;
  final highAlerts = alerts.where((a) => a.severity == 'HIGH').length;
  final openAlerts = alerts.where((a) => a.status == 'OPEN').length;
  
  // System health average
  final avgHealthScore = properties.isNotEmpty
      ? properties.map((p) => p.healthScore).reduce((a, b) => a + b) / properties.length
      : 0.0;
  
  // System status overview
  final systemStatuses = overview.systemStatuses;
  final totalSystems = systemStatuses.fold<int>(0, (sum, status) => sum + status.total);
  final operationalSystems = systemStatuses.fold<int>(0, (sum, status) => sum + status.operational);
  final warningSystems = systemStatuses.fold<int>(0, (sum, status) => sum + status.warning);
  final criticalSystems = systemStatuses.fold<int>(0, (sum, status) => sum + status.critical);
  final offlineSystems = systemStatuses.fold<int>(0, (sum, status) => sum + status.offline);
  
  return {
    // Property stats
    'totalProperties': totalProperties,
    'healthyProperties': healthyProperties,
    'warningProperties': warningProperties,
    'criticalProperties': criticalProperties,
    'offlineProperties': offlineProperties,
    'healthPercentage': totalProperties > 0 ? (healthyProperties / totalProperties) * 100 : 0,
    
    // Alert stats
    'totalAlerts': totalAlerts,
    'criticalAlerts': criticalAlerts,
    'highAlerts': highAlerts,
    'openAlerts': openAlerts,
    'alertsResolutionRate': totalAlerts > 0 ? ((totalAlerts - openAlerts) / totalAlerts) * 100 : 0,
    
    // System stats
    'avgHealthScore': avgHealthScore,
    'totalSystems': totalSystems,
    'operationalSystems': operationalSystems,
    'warningSystems': warningSystems,
    'criticalSystems': criticalSystems,
    'offlineSystems': offlineSystems,
    'systemHealthPercentage': totalSystems > 0 ? (operationalSystems / totalSystems) * 100 : 0,
    
    // Performance metrics
    'uptime': overview.statistics.metrics.uptime,
    'incidents': overview.statistics.metrics.incidents,
    'resolved': overview.statistics.metrics.resolved,
    'avgResponseTime': overview.statistics.metrics.avgResponseTime,
    'resolutionRate': overview.statistics.metrics.resolutionRate,
    'openIncidents': overview.statistics.metrics.openIncidents,
  };
}

// Mock data creation function for fallback
DashboardOverview _createMockDashboardOverview() {
  return DashboardOverview(
    summary: const DashboardSummary(
      totalProperties: 5,
      activeProperties: 4,
      totalAlerts: 3,
      criticalAlerts: 1,
      systemHealth: 85.5,
    ),
    properties: [
      const PropertySummary(
        id: 'prop-1',
        name: 'Sunrise Apartments',
        type: 'RESIDENTIAL',
        status: 'OPERATIONAL',
        healthScore: 92.5,
        alertCount: 0,
        lastUpdate: '2024-01-15T10:30:00Z',
      ),
      const PropertySummary(
        id: 'prop-2',
        name: 'Downtown Office Complex',
        type: 'COMMERCIAL',
        status: 'WARNING',
        healthScore: 78.2,
        alertCount: 2,
        lastUpdate: '2024-01-15T09:45:00Z',
      ),
      const PropertySummary(
        id: 'prop-3',
        name: 'Industrial Park A',
        type: 'INDUSTRIAL',
        status: 'CRITICAL',
        healthScore: 65.8,
        alertCount: 5,
        lastUpdate: '2024-01-15T08:15:00Z',
      ),
    ],
    recentAlerts: [
      Alert(
        id: 'alert-1',
        title: 'HVAC System Malfunction',
        message: 'Air conditioning unit in Building A is not responding',
        severity: 'CRITICAL',
        status: 'OPEN',
        createdAt: '2024-01-15T09:30:00Z',
        updatedAt: '2024-01-15T09:30:00Z',
        property: const PropertyInfo(
          id: 'prop-3',
          name: 'Industrial Park A',
          type: 'INDUSTRIAL',
        ),
      ),
      Alert(
        id: 'alert-2',
        title: 'Security Camera Offline',
        message: 'Camera #5 in parking lot is not responding',
        severity: 'MEDIUM',
        status: 'ACKNOWLEDGED',
        createdAt: '2024-01-15T08:45:00Z',
        updatedAt: '2024-01-15T09:00:00Z',
        property: const PropertyInfo(
          id: 'prop-2',
          name: 'Downtown Office Complex',
          type: 'COMMERCIAL',
        ),
      ),
    ],
    systemStatuses: const [
      SystemStatusOverview(
        systemType: 'ELECTRICITY',
        operational: 12,
        warning: 2,
        critical: 1,
        offline: 0,
        total: 15,
      ),
      SystemStatusOverview(
        systemType: 'WATER',
        operational: 8,
        warning: 1,
        critical: 0,
        offline: 1,
        total: 10,
      ),
      SystemStatusOverview(
        systemType: 'SECURITY',
        operational: 18,
        warning: 3,
        critical: 1,
        offline: 0,
        total: 22,
      ),
      SystemStatusOverview(
        systemType: 'INTERNET',
        operational: 5,
        warning: 0,
        critical: 0,
        offline: 0,
        total: 5,
      ),
    ],
    activities: const [
      Activity(
        id: 'activity-1',
        action: 'ALERT_CREATED',
        description: 'New critical alert created for HVAC system',
        createdAt: '2024-01-15T09:30:00Z',
      ),
      Activity(
        id: 'activity-2',
        action: 'MAINTENANCE_SCHEDULED',
        description: 'Scheduled maintenance for elevator system',
        createdAt: '2024-01-15T08:00:00Z',
      ),
    ],
    statistics: const DashboardStatistics(
      timeRange: '24h',
      metrics: DashboardMetrics(
        uptime: 98.5,
        incidents: 8,
        resolved: 6,
        avgResponseTime: 2.5,
      ),
      trends: [
        TrendData(date: '2024-01-14', value: 97.2),
        TrendData(date: '2024-01-15', value: 98.5),
      ],
    ),
  );
}
