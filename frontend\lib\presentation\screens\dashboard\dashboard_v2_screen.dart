import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/services/service_locator.dart';
import '../../routes/app_router.dart';
import '../../providers/dashboard_providers.dart';
import '../main/main_navigation_screen.dart';
import 'widgets/dashboard_header.dart';
import 'widgets/system_status_card.dart';
import 'widgets/property_grid.dart';
import 'widgets/alerts_feed.dart';
import 'widgets/system_health_chart.dart';
import 'widgets/quick_actions.dart';
import 'widgets/performance_metrics.dart';

class DashboardV2Screen extends ConsumerStatefulWidget {
  const DashboardV2Screen({super.key});

  @override
  ConsumerState<DashboardV2Screen> createState() => _DashboardV2ScreenState();
}

class _DashboardV2ScreenState extends ConsumerState<DashboardV2Screen> {
  Timer? _autoRefreshTimer;

  @override
  void dispose() {
    _autoRefreshTimer?.cancel();
    super.dispose();
  }

  void _startAutoRefresh() {
    _stopAutoRefresh(); // Cancel any existing timer
    _autoRefreshTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      _handleRefresh();
    });
  }

  void _stopAutoRefresh() {
    _autoRefreshTimer?.cancel();
    _autoRefreshTimer = null;
  }

  @override
  Widget build(BuildContext context) {
    // Check authentication first
    if (!serviceLocator.authService.isAuthenticated) {
      // User is not authenticated, redirect to login
      WidgetsBinding.instance.addPostFrameCallback((_) {
        context.go(AppRoutes.login);
      });
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    final timeRange = ref.watch(dashboardTimeRangeProvider);
    final propertyFilter = ref.watch(propertyFilterProvider);
    final isRefreshing = ref.watch(dashboardRefreshProvider);

    // Setup auto-refresh listener
    ref.listen(autoRefreshProvider, (previous, next) {
      if (next) {
        _startAutoRefresh();
      } else {
        _stopAutoRefresh();
      }
    });

    final dashboardParams = DashboardParams(
      propertyIds: propertyFilter,
      timeRange: timeRange,
    );

    return Scaffold(
      appBar: AppBar(
        title: const Text('Dashboard V2'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
        elevation: 0,
        actions: [
          // Version Toggle
          Container(
            margin: const EdgeInsets.only(right: 8),
            child: TextButton.icon(
              onPressed: () {
                ref.read(dashboardVersionProvider.notifier).toggleVersion();
              },
              icon: const Icon(Icons.arrow_back, size: 18),
              label: const Text('V1', style: TextStyle(fontSize: 12)),
              style: TextButton.styleFrom(
                foregroundColor: Colors.grey[600],
                backgroundColor: Colors.grey[100],
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                minimumSize: const Size(0, 32),
              ),
            ),
          ),

          // Time Range Selector
          PopupMenuButton<String>(
            icon: const Icon(Icons.schedule),
            onSelected: (value) {
              ref.read(dashboardTimeRangeProvider.notifier).setTimeRange(value);
            },
            itemBuilder: (context) => [
              const PopupMenuItem(value: '24h', child: Text('Last 24 Hours')),
              const PopupMenuItem(value: '7d', child: Text('Last 7 Days')),
              const PopupMenuItem(value: '30d', child: Text('Last 30 Days')),
              const PopupMenuItem(value: '90d', child: Text('Last 90 Days')),
            ],
          ),
          
          // Auto Refresh Toggle
          Consumer(
            builder: (context, ref, child) {
              final autoRefresh = ref.watch(autoRefreshProvider);
              return IconButton(
                icon: Icon(
                  autoRefresh ? Icons.sync : Icons.sync_disabled,
                  color: autoRefresh ? AppTheme.primaryColor : Colors.grey,
                ),
                onPressed: () {
                  ref.read(autoRefreshProvider.notifier).toggle();
                },
                tooltip: autoRefresh ? 'Disable Auto Refresh' : 'Enable Auto Refresh',
              );
            },
          ),
          
          // Manual Refresh
          IconButton(
            icon: isRefreshing 
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.refresh),
            onPressed: isRefreshing ? null : () => _handleRefresh(),
            tooltip: 'Refresh Data',
          ),
          
          // Settings
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => _showDashboardSettings(),
            tooltip: 'Dashboard Settings',
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _handleRefresh,
        child: ref.watch(dashboardOverviewProvider(dashboardParams)).when(
          data: (overview) => _buildDashboardContent(overview),
          loading: () => _buildLoadingState(),
          error: (error, stack) => _buildErrorState(error),
        ),
      ),
    );
  }

  Widget _buildDashboardContent(overview) {
    final stats = ref.watch(dashboardStatsProvider(overview));
    
    return SingleChildScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Dashboard Header with Key Metrics
          DashboardHeader(overview: overview, stats: stats),
          
          const SizedBox(height: 20),
          
          // Quick Actions
          const QuickActions(),
          
          const SizedBox(height: 20),
          
          // System Status Overview
          SystemStatusCard(
            systemStatuses: overview.systemStatuses,
            stats: stats,
          ),
          
          const SizedBox(height: 20),
          
          // Properties Grid and System Health Chart Row
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Properties Grid (60% width)
              Expanded(
                flex: 3,
                child: PropertyGrid(
                  properties: overview.properties,
                  onPropertyTap: _navigateToProperty,
                ),
              ),
              
              const SizedBox(width: 16),
              
              // System Health Chart (40% width)
              Expanded(
                flex: 2,
                child: SystemHealthChart(
                  systemStatuses: overview.systemStatuses,
                  stats: stats,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 20),
          
          // Performance Metrics
          PerformanceMetrics(
            metrics: overview.statistics.metrics,
            timeRange: ref.watch(dashboardTimeRangeProvider),
          ),
          
          const SizedBox(height: 20),
          
          // Alerts Feed
          AlertsFeed(
            alerts: overview.recentAlerts,
            onAlertTap: _navigateToAlert,
            onViewAllTap: _navigateToAllAlerts,
          ),
          
          const SizedBox(height: 20),
          
          // Recent Activities
          _buildRecentActivities(overview.activities),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text('Loading dashboard data...'),
        ],
      ),
    );
  }

  Widget _buildErrorState(Object error) {
    final errorString = error.toString();
    final isAuthError = errorString.contains('Authentication required') ||
                       errorString.contains('UNAUTHORIZED') ||
                       errorString.contains('401');

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            isAuthError ? Icons.lock_outline : Icons.error_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            isAuthError ? 'Authentication Required' : 'Failed to load dashboard',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            isAuthError
                ? 'Please log in to access Dashboard V2 features'
                : error.toString(),
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          if (isAuthError) ...[
            ElevatedButton.icon(
              onPressed: () {
                // Switch back to V1 and redirect to login
                ref.read(dashboardVersionProvider.notifier).toggleVersion();
                context.go(AppRoutes.login);
              },
              icon: const Icon(Icons.login),
              label: const Text('Go to Login'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
              ),
            ),
            const SizedBox(height: 12),
            TextButton.icon(
              onPressed: () {
                // Switch back to V1
                ref.read(dashboardVersionProvider.notifier).toggleVersion();
              },
              icon: const Icon(Icons.arrow_back),
              label: const Text('Back to Dashboard V1'),
            ),
          ] else ...[
            ElevatedButton.icon(
              onPressed: _handleRefresh,
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildRecentActivities(activities) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recent Activities',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    // TODO: Navigate to activities screen
                  },
                  child: const Text('View All'),
                ),
              ],
            ),
            const SizedBox(height: 12),
            ...activities.take(5).map((activity) => _buildActivityItem(activity)),
          ],
        ),
      ),
    );
  }

  Widget _buildActivityItem(activity) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              _getActivityIcon(activity.action),
              color: AppTheme.primaryColor,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  activity.description,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  '${activity.user?.name ?? 'System'} • ${_formatTime(activity.createdAt)}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  IconData _getActivityIcon(String action) {
    switch (action.toUpperCase()) {
      case 'SYSTEM_INITIALIZATION':
        return Icons.power_settings_new;
      case 'ALERT_CREATED':
        return Icons.warning;
      case 'ALERT_RESOLVED':
        return Icons.check_circle;
      case 'MAINTENANCE_SCHEDULED':
        return Icons.schedule;
      case 'MAINTENANCE_COMPLETED':
        return Icons.build_circle;
      case 'USER_LOGIN':
        return Icons.login;
      case 'USER_LOGOUT':
        return Icons.logout;
      case 'PROPERTY_UPDATED':
        return Icons.edit;
      case 'SYSTEM_STATUS_CHANGED':
        return Icons.sync;
      default:
        return Icons.info;
    }
  }

  String _formatTime(String timestamp) {
    final dateTime = DateTime.parse(timestamp);
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }

  Future<void> _handleRefresh() async {
    await ref.read(dashboardRefreshProvider.notifier).refresh();
    ref.invalidate(dashboardOverviewProvider);
    ref.invalidate(dashboardAlertsProvider);
  }

  void _navigateToProperty(String propertyId) {
    context.go('/properties/$propertyId');
  }

  void _navigateToAlert(String alertId) {
    // TODO: Navigate to alert detail
  }

  void _navigateToAllAlerts() {
    // TODO: Navigate to alerts screen
  }

  void _showDashboardSettings() {
    showModalBottomSheet(
      context: context,
      builder: (context) => _buildDashboardSettings(),
    );
  }

  Widget _buildDashboardSettings() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Dashboard Settings',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          
          // Auto Refresh Setting
          Consumer(
            builder: (context, ref, child) {
              final autoRefresh = ref.watch(autoRefreshProvider);
              return SwitchListTile(
                title: const Text('Auto Refresh'),
                subtitle: const Text('Automatically refresh data every 30 seconds'),
                value: autoRefresh,
                onChanged: (value) {
                  ref.read(autoRefreshProvider.notifier).setEnabled(value);
                },
              );
            },
          ),
          
          // Clear Cache Button
          ListTile(
            leading: const Icon(Icons.clear_all),
            title: const Text('Clear Cache'),
            subtitle: const Text('Clear all cached dashboard data'),
            onTap: () async {
              // TODO: Clear cache
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Cache cleared successfully')),
              );
            },
          ),
        ],
      ),
    );
  }
}
