import 'package:flutter/material.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../data/models/dashboard.dart';

class PropertyGrid extends StatelessWidget {
  final List<PropertySummary> properties;
  final Function(String) onPropertyTap;

  const PropertyGrid({
    super.key,
    required this.properties,
    required this.onPropertyTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.business,
                  size: 24,
                  color: AppTheme.primaryColor,
                ),
                const SizedBox(width: 12),
                Text(
                  'Properties',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                Text(
                  '${properties.length} total',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 20),
            
            // Properties List
            if (properties.isEmpty)
              _buildEmptyState(context)
            else
              ...properties.map((property) => _buildPropertyItem(context, property)),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(32),
      child: Column(
        children: [
          Icon(
            Icons.business_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No Properties Found',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Add properties to see them here',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPropertyItem(BuildContext context, PropertySummary property) {
    final statusColor = _getStatusColor(property.status);
    final healthPercentage = property.healthScore.toDouble();
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () => onPropertyTap(property.id),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: statusColor.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: statusColor.withValues(alpha: 0.2),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Property Header
              Row(
                children: [
                  // Property Icon
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: _getPropertyTypeColor(property.type).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _getPropertyTypeIcon(property.type),
                      size: 20,
                      color: _getPropertyTypeColor(property.type),
                    ),
                  ),
                  
                  const SizedBox(width: 12),
                  
                  // Property Name and Type
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          property.name,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 2),
                        Text(
                          _getPropertyTypeDisplayName(property.type),
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  // Status Badge
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: statusColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          width: 6,
                          height: 6,
                          decoration: BoxDecoration(
                            color: statusColor,
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          _getStatusDisplayName(property.status),
                          style: TextStyle(
                            color: statusColor,
                            fontSize: 11,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // Health Score Progress
              Row(
                children: [
                  Text(
                    'Health Score',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                  const Spacer(),
                  Text(
                    '${property.healthScore}%',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: _getHealthColor(healthPercentage),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 8),
              
              LinearProgressIndicator(
                value: healthPercentage / 100,
                backgroundColor: Colors.grey[200],
                valueColor: AlwaysStoppedAnimation<Color>(
                  _getHealthColor(healthPercentage),
                ),
                minHeight: 6,
              ),
              
              const SizedBox(height: 16),
              
              // Property Stats
              Row(
                children: [
                  _buildStatItem(
                    context,
                    Icons.warning_outlined,
                    '${property.alertCount}',
                    'Alerts',
                    property.alertCount > 0 ? AppTheme.warningColor : Colors.grey[600]!,
                  ),
                  const SizedBox(width: 24),
                  _buildStatItem(
                    context,
                    Icons.access_time,
                    _formatLastUpdate(property.lastUpdate),
                    'Last Update',
                    Colors.grey[600]!,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatItem(
    BuildContext context,
    IconData icon,
    String value,
    String label,
    Color color,
  ) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 16, color: color),
        const SizedBox(width: 4),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              value,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: color,
              ),
            ),
            Text(
              label,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
                fontSize: 10,
              ),
            ),
          ],
        ),
      ],
    );
  }

  IconData _getPropertyTypeIcon(String type) {
    switch (type.toUpperCase()) {
      case 'RESIDENTIAL':
        return Icons.home;
      case 'COMMERCIAL':
        return Icons.business;
      case 'INDUSTRIAL':
        return Icons.factory;
      case 'MIXED_USE':
        return Icons.apartment;
      default:
        return Icons.business;
    }
  }

  Color _getPropertyTypeColor(String type) {
    switch (type.toUpperCase()) {
      case 'RESIDENTIAL':
        return Colors.blue;
      case 'COMMERCIAL':
        return Colors.green;
      case 'INDUSTRIAL':
        return Colors.orange;
      case 'MIXED_USE':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  String _getPropertyTypeDisplayName(String type) {
    switch (type.toUpperCase()) {
      case 'RESIDENTIAL':
        return 'Residential';
      case 'COMMERCIAL':
        return 'Commercial';
      case 'INDUSTRIAL':
        return 'Industrial';
      case 'MIXED_USE':
        return 'Mixed Use';
      default:
        return type;
    }
  }

  Color _getStatusColor(String status) {
    switch (status.toUpperCase()) {
      case 'OPERATIONAL':
        return AppTheme.successColor;
      case 'WARNING':
        return AppTheme.warningColor;
      case 'CRITICAL':
        return AppTheme.errorColor;
      case 'OFFLINE':
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }

  String _getStatusDisplayName(String status) {
    switch (status.toUpperCase()) {
      case 'OPERATIONAL':
        return 'Operational';
      case 'WARNING':
        return 'Warning';
      case 'CRITICAL':
        return 'Critical';
      case 'OFFLINE':
        return 'Offline';
      default:
        return status;
    }
  }

  Color _getHealthColor(double percentage) {
    if (percentage >= 90) return AppTheme.successColor;
    if (percentage >= 75) return AppTheme.infoColor;
    if (percentage >= 50) return AppTheme.warningColor;
    return AppTheme.errorColor;
  }

  String _formatLastUpdate(String timestamp) {
    final dateTime = DateTime.parse(timestamp);
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'Now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h';
    } else {
      return '${difference.inDays}d';
    }
  }
}
