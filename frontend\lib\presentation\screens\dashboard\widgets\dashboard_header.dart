import 'package:flutter/material.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../data/models/dashboard.dart';

class DashboardHeader extends StatelessWidget {
  final DashboardOverview overview;
  final Map<String, dynamic> stats;

  const DashboardHeader({
    super.key,
    required this.overview,
    required this.stats,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Title
            Row(
              children: [
                Icon(
                  Icons.dashboard,
                  size: 28,
                  color: AppTheme.primaryColor,
                ),
                const SizedBox(width: 12),
                Text(
                  'System Overview',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                const Spacer(),
                _buildHealthBadge(context),
              ],
            ),
            
            const SizedBox(height: 20),
            
            // Key Metrics Row - Responsive
            LayoutBuilder(
              builder: (context, constraints) {
                if (constraints.maxWidth > 800) {
                  // Large screens: 4 columns
                  return Row(
                    children: [
                      Expanded(
                        child: _buildMetricCard(
                          context,
                          'Properties',
                          '${stats['totalProperties']}',
                          '${stats['healthyProperties']} healthy',
                          Icons.business,
                          AppTheme.primaryColor,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildMetricCard(
                          context,
                          'Systems',
                          '${stats['totalSystems']}',
                          '${stats['operationalSystems']} operational',
                          Icons.settings,
                          AppTheme.successColor,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildMetricCard(
                          context,
                          'Alerts',
                          '${stats['totalAlerts']}',
                          '${stats['criticalAlerts']} critical',
                          Icons.warning,
                          stats['criticalAlerts'] > 0 ? AppTheme.errorColor : AppTheme.warningColor,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildMetricCard(
                          context,
                          'Uptime',
                          '${stats['uptime'].toStringAsFixed(1)}%',
                          'Last 24h',
                          Icons.trending_up,
                          AppTheme.infoColor,
                        ),
                      ),
                    ],
                  );
                } else {
                  // Small screens: 2x2 grid
                  return Column(
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: _buildMetricCard(
                              context,
                              'Properties',
                              '${stats['totalProperties']}',
                              '${stats['healthyProperties']} healthy',
                              Icons.business,
                              AppTheme.primaryColor,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: _buildMetricCard(
                              context,
                              'Systems',
                              '${stats['totalSystems']}',
                              '${stats['operationalSystems']} operational',
                              Icons.settings,
                              AppTheme.successColor,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          Expanded(
                            child: _buildMetricCard(
                              context,
                              'Alerts',
                              '${stats['totalAlerts']}',
                              '${stats['criticalAlerts']} critical',
                              Icons.warning,
                              stats['criticalAlerts'] > 0 ? AppTheme.errorColor : AppTheme.warningColor,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: _buildMetricCard(
                              context,
                              'Uptime',
                              '${stats['uptime'].toStringAsFixed(1)}%',
                              'Last 24h',
                              Icons.trending_up,
                              AppTheme.infoColor,
                            ),
                          ),
                        ],
                      ),
                    ],
                  );
                }
              },
            ),
            
            const SizedBox(height: 20),
            
            // System Health Progress Bar
            _buildSystemHealthProgress(context),
          ],
        ),
      ),
    );
  }

  Widget _buildHealthBadge(BuildContext context) {
    final healthPercentage = stats['systemHealthPercentage'] as double;
    Color badgeColor;
    String badgeText;
    IconData badgeIcon;

    if (healthPercentage >= 90) {
      badgeColor = AppTheme.successColor;
      badgeText = 'Excellent';
      badgeIcon = Icons.check_circle;
    } else if (healthPercentage >= 75) {
      badgeColor = AppTheme.infoColor;
      badgeText = 'Good';
      badgeIcon = Icons.info;
    } else if (healthPercentage >= 50) {
      badgeColor = AppTheme.warningColor;
      badgeText = 'Warning';
      badgeIcon = Icons.warning;
    } else {
      badgeColor = AppTheme.errorColor;
      badgeText = 'Critical';
      badgeIcon = Icons.error;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: badgeColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: badgeColor.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(badgeIcon, size: 16, color: badgeColor),
          const SizedBox(width: 4),
          Text(
            badgeText,
            style: TextStyle(
              color: badgeColor,
              fontWeight: FontWeight.w600,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMetricCard(
    BuildContext context,
    String title,
    String value,
    String subtitle,
    IconData icon,
    Color color,
  ) {
    return ConstrainedBox(
      constraints: const BoxConstraints(
        minWidth: 120,
        minHeight: 100,
      ),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.05),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withValues(alpha: 0.1)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            subtitle,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
            ),
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
        ],
      ),
    );
  }

  Widget _buildSystemHealthProgress(BuildContext context) {
    final healthPercentage = stats['systemHealthPercentage'] as double;
    final operationalSystems = stats['operationalSystems'] as int;
    final totalSystems = stats['totalSystems'] as int;
    final warningSystems = stats['warningSystems'] as int;
    final criticalSystems = stats['criticalSystems'] as int;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Text(
                'Overall System Health',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            Text(
              '${healthPercentage.toStringAsFixed(1)}%',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: _getHealthColor(healthPercentage),
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 12),
        
        // Multi-segment progress bar
        Container(
          height: 8,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            color: Colors.grey[200],
          ),
          child: Row(
            children: [
              // Operational segment
              if (operationalSystems > 0)
                Expanded(
                  flex: operationalSystems,
                  child: Container(
                    decoration: BoxDecoration(
                      color: AppTheme.successColor,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ),
              // Warning segment
              if (warningSystems > 0)
                Expanded(
                  flex: warningSystems,
                  child: Container(
                    decoration: BoxDecoration(
                      color: AppTheme.warningColor,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ),
              // Critical segment
              if (criticalSystems > 0)
                Expanded(
                  flex: criticalSystems,
                  child: Container(
                    decoration: BoxDecoration(
                      color: AppTheme.errorColor,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ),
            ],
          ),
        ),
        
        const SizedBox(height: 8),
        
        // Legend
        Wrap(
          spacing: 16,
          runSpacing: 8,
          children: [
            _buildLegendItem('Operational', AppTheme.successColor, operationalSystems),
            _buildLegendItem('Warning', AppTheme.warningColor, warningSystems),
            _buildLegendItem('Critical', AppTheme.errorColor, criticalSystems),
          ],
        ),
      ],
    );
  }

  Widget _buildLegendItem(String label, Color color, int count) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(width: 4),
        Text(
          '$label ($count)',
          style: const TextStyle(
            fontSize: 12,
            color: Colors.grey,
          ),
        ),
      ],
    );
  }

  Color _getHealthColor(double percentage) {
    if (percentage >= 90) return AppTheme.successColor;
    if (percentage >= 75) return AppTheme.infoColor;
    if (percentage >= 50) return AppTheme.warningColor;
    return AppTheme.errorColor;
  }
}
